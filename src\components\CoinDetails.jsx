import React, { useState } from 'react';
import { useLanguage } from '../hooks/useLanguage';

const CoinDetails = ({ 
  selectedCoin, 
  currentPrice, 
  onUpdateTargets, 
  isAutoRefresh 
}) => {
  const { t } = useLanguage();
  const [sellTargets, setSellTargets] = useState(
    selectedCoin?.sellTargets || [null, null, null]
  );
  const [stopLoss, setStopLoss] = useState(selectedCoin?.stopLoss || '');

  React.useEffect(() => {
    if (selectedCoin) {
      setSellTargets(selectedCoin.sellTargets || [null, null, null]);
      setStopLoss(selectedCoin.stopLoss || '');
    }
  }, [selectedCoin]);

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 8
    }).format(value);
  };

  const formatNumber = (value, decimals = 8) => {
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: decimals
    }).format(value);
  };

  const handleSaveTargets = () => {
    if (selectedCoin) {
      onUpdateTargets(selectedCoin.id, sellTargets, parseFloat(stopLoss) || null);
    }
  };

  const handleTargetChange = (index, value) => {
    const newTargets = [...sellTargets];
    newTargets[index] = parseFloat(value) || null;
    setSellTargets(newTargets);
  };

  if (!selectedCoin) {
    return (
      <div className="sidebar">
        <div className="card-header">
          <h4 className="card-title">{t('entryDetails')}</h4>
        </div>
        <p style={{ color: 'var(--text-muted)', textAlign: 'center', padding: 'var(--spacing-lg)' }}>
          {t('noCoinSelected')}
        </p>
      </div>
    );
  }

  const currentValue = selectedCoin.quantity * currentPrice;
  const pl = currentValue - selectedCoin.totalInvested;
  const plPercent = selectedCoin.totalInvested > 0 ? (pl / selectedCoin.totalInvested) * 100 : 0;

  const getPLClass = (value) => {
    if (value > 0) return 'profit';
    if (value < 0) return 'loss';
    return 'neutral';
  };

  return (
    <div className="sidebar">
      <div className="card-header">
        <h4 className="card-title">{t('entryDetails')}</h4>
        <div style={{ fontSize: '0.875rem', color: 'var(--text-muted)' }}>
          {selectedCoin.name} ({selectedCoin.symbol})
        </div>
      </div>

      {/* Current Status */}
      <div className="mb-6">
        <h5>{t('currentStatus')}</h5>
        <div className="grid gap-4">
          <div className="flex justify-between">
            <span className="text-secondary">{t('avgPrice')}:</span>
            <span>{formatCurrency(selectedCoin.avgEntry)}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-secondary">{t('invested')}:</span>
            <span>{formatCurrency(selectedCoin.totalInvested)}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-secondary">{t('quantity')}:</span>
            <span>{formatNumber(selectedCoin.quantity)}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-secondary">{t('currentPrice')}:</span>
            <span>{formatCurrency(currentPrice)}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-secondary">{t('currentValue')}:</span>
            <span>{formatCurrency(currentValue)}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-secondary">{t('plDollar')}:</span>
            <span className={getPLClass(pl)}>{formatCurrency(pl)}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-secondary">{t('plPercent')}:</span>
            <span className={getPLClass(pl)}>
              {plPercent >= 0 ? '+' : ''}{plPercent.toFixed(2)}%
            </span>
          </div>
        </div>
      </div>

      {/* Auto-refresh indicator */}
      <div className="mb-6">
        <div className="flex items-center gap-2">
          <div
            className="w-2 h-2 rounded-full"
            style={{
              backgroundColor: isAutoRefresh ? 'var(--success-color)' : 'var(--text-muted)'
            }}
          ></div>
          <span style={{ fontSize: '0.875rem', color: 'var(--text-muted)' }}>
            {t('autoUpdate')}
          </span>
        </div>
      </div>

      {/* Sell Targets */}
      <div className="mb-6">
        <h5>{t('sellTargets')}</h5>
        <div className="grid gap-2">
          {sellTargets.map((target, index) => (
            <div key={index} className="form-group">
              <label className="form-label">{t('target')} {index + 1}</label>
              <input
                type="number"
                className="form-input"
                value={target || ''}
                onChange={(e) => handleTargetChange(index, e.target.value)}
                placeholder="0.00"
                step="0.00000001"
              />
            </div>
          ))}
        </div>
      </div>

      {/* Stop Loss */}
      <div className="mb-6">
        <div className="form-group">
          <label className="form-label">{t('stopLoss')}</label>
          <input
            type="number"
            className="form-input"
            value={stopLoss}
            onChange={(e) => setStopLoss(e.target.value)}
            placeholder="0.00"
            step="0.00000001"
          />
        </div>
      </div>

      {/* Save Button */}
      <button 
        className="btn btn-primary"
        onClick={handleSaveTargets}
        style={{ width: '100%' }}
      >
        {t('save')} {t('sellTargets')}
      </button>
    </div>
  );
};

export default CoinDetails;
