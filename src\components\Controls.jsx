import React, { useState } from 'react';
import { useLanguage } from '../hooks/useLanguage';

const Controls = ({ 
  onAddCoin, 
  onRefreshPrices, 
  onImportTxt, 
  onSaveAsTxt, 
  onDeleteSelected,
  availableCoins,
  isLoading 
}) => {
  const { t } = useLanguage();
  const [selectedCoin, setSelectedCoin] = useState('');
  const [quantity, setQuantity] = useState('');
  const [investedAmount, setInvestedAmount] = useState('');

  const handleAddCoin = () => {
    if (selectedCoin && quantity && investedAmount) {
      const qty = parseFloat(quantity);
      const amount = parseFloat(investedAmount);

      if (qty > 0 && amount > 0) {
        onAddCoin(selectedCoin, qty, amount);
        setQuantity('');
        setInvestedAmount('');
      } else {
        alert(t('validValues'));
      }
    }
  };

  const handleFileImport = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        onImportTxt(e.target.result);
      };
      reader.readAsText(file);
    }
  };

  return (
    <div className="controls-bar">
      <div className="controls-group">
        <div className="form-group">
          <label className="form-label">{t('activeCoin')}</label>
          <select 
            className="form-select"
            value={selectedCoin}
            onChange={(e) => setSelectedCoin(e.target.value)}
          >
            <option value="">{t('addSearch')}</option>
            {Object.entries(availableCoins).map(([symbol, coin]) => (
              <option key={symbol} value={symbol}>
                {coin.name} ({symbol})
              </option>
            ))}
          </select>
        </div>
      </div>

      <div className="controls-group">
        <div className="form-group">
          <label className="form-label">{t('quantity')}</label>
          <input
            type="number"
            className="form-input"
            value={quantity}
            onChange={(e) => setQuantity(e.target.value)}
            placeholder="0.00"
            step="0.00000001"
          />
        </div>
      </div>

      <div className="controls-group">
        <div className="form-group">
          <label className="form-label">{t('invested')} ($)</label>
          <input
            type="number"
            className="form-input"
            value={investedAmount}
            onChange={(e) => setInvestedAmount(e.target.value)}
            placeholder="0.00"
            step="0.01"
          />
        </div>
      </div>

      <div className="controls-group">
        <button 
          className="btn btn-primary"
          onClick={handleAddCoin}
          disabled={!selectedCoin || !quantity || !investedAmount || isLoading}
        >
          ➕ {t('addReplace')}
        </button>
      </div>

      <div className="controls-group">
        <button 
          className="btn btn-secondary"
          onClick={onRefreshPrices}
          disabled={isLoading}
        >
          {isLoading ? <div className="spinner"></div> : '🔄'} {t('refreshPrices')}
        </button>
      </div>

      <div className="controls-group">
        <input
          type="file"
          accept=".txt"
          onChange={handleFileImport}
          style={{ display: 'none' }}
          id="import-file"
        />
        <label htmlFor="import-file" className="btn btn-secondary">
          📥 {t('importTxt')}
        </label>
      </div>

      <div className="controls-group">
        <button 
          className="btn btn-secondary"
          onClick={onSaveAsTxt}
        >
          📤 {t('saveAsTxt')}
        </button>
      </div>

      <div className="controls-group">
        <button 
          className="btn btn-danger"
          onClick={onDeleteSelected}
        >
          🗑️ {t('delete')}
        </button>
      </div>
    </div>
  );
};

export default Controls;
