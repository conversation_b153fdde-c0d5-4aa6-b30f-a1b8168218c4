# Crypto Portfolio Tracker | متتبع محفظة العملات المشفرة

A dual-language cryptocurrency investment portfolio tracker with Arabic and English support, featuring RTL/LTR layouts and a professional dark theme.

## Features

### 🌐 Dual Language Support
- **English (LTR)** and **Arabic (RTL)** with seamless switching
- Complete UI translation including all labels, buttons, and messages
- Automatic direction switching (RTL/LTR) based on selected language
- Language preference saved in localStorage

### 📊 Portfolio Management
- **Main Portfolio Table** with comprehensive coin tracking
- **Real-time Price Updates** (simulated every 30 seconds)
- **Profit/Loss Calculations** in both dollar amounts and percentages
- **Portfolio Summary** with total invested, current value, and overall P/L

### 💰 Dollar Cost Averaging (DCA)
- **DCA Table** supporting up to 10 entries per coin
- **Individual Entry Tracking** with price, quantity, and P/L per entry
- **Average Entry Price** calculation across all DCA entries
- **Entry Management** with ability to delete individual DCA entries

### 🎯 Trading Tools
- **Sell Targets** - Set up to 3 price targets per coin
- **Stop Loss** - Configure stop loss prices
- **Coin Details Sidebar** with comprehensive information
- **Auto-refresh** indicator for real-time updates

### 📁 Data Management
- **Import/Export TXT** functionality for portfolio backup
- **Local Storage** persistence for portfolio data
- **Sample Data** included for demonstration

### 🎨 Design & UX
- **Professional Dark Theme** with modern color scheme
- **Responsive Design** - Desktop-first with mobile optimization
- **Clean Typography** using Inter (English) and Cairo (Arabic) fonts
- **Intuitive Controls** with clear visual feedback
- **Profit/Loss Color Coding** (Green for profit, Red for loss)

## Technology Stack

- **React 18** with Hooks for state management
- **Vite** for fast development and building
- **CSS Custom Properties** for theming
- **Google Fonts** (Inter & Cairo) for typography
- **Modern JavaScript** (ES2020+)

## Getting Started

### Prerequisites
- Node.js 16+ 
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd crypto-portfolio-tracker
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:3000`

### Building for Production

```bash
npm run build
```

## Usage

### Adding Coins
1. Select a coin from the "Active Coin" dropdown
2. Enter the quantity and invested amount
3. Click "Add/Replace" to add to your portfolio

### Language Switching
- Use the toggle switch in the top-right corner
- Switch between English (EN) and Arabic (العربية)
- The entire interface will adapt to the selected language and direction

### Setting Targets
1. Click on any coin in the portfolio table
2. Use the sidebar to set up to 3 sell targets
3. Configure a stop loss price
4. Click "Save Sell Targets" to apply

### DCA Management
- View all DCA entries in the dedicated table below the main portfolio
- Each entry shows individual P/L performance
- Delete entries using the trash icon (minimum 1 entry required)

### Data Export/Import
- **Export**: Click "Save as TXT" to download your portfolio
- **Import**: Click "Import TXT" and select a previously exported file
- Format: `SYMBOL,QUANTITY,INVESTED_AMOUNT` per line

## Supported Cryptocurrencies

The application includes mock data for popular cryptocurrencies:
- Bitcoin (BTC)
- Ethereum (ETH)
- Cardano (ADA)
- Solana (SOL)
- Polkadot (DOT)
- Chainlink (LINK)
- Polygon (MATIC)
- Avalanche (AVAX)

## File Structure

```
src/
├── components/          # React components
│   ├── LanguageToggle.jsx
│   ├── Controls.jsx
│   ├── PortfolioTable.jsx
│   ├── CoinDetails.jsx
│   └── DCATable.jsx
├── hooks/              # Custom React hooks
│   ├── useLanguage.js
│   └── useCrypto.js
├── utils/              # Utility functions
│   └── translations.js
├── style.css           # Global styles
├── main.jsx           # Application entry point
└── App.jsx            # Main application component
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Future Enhancements

- Real cryptocurrency API integration
- Historical price charts
- Portfolio performance analytics
- Additional language support
- Mobile app version
- Advanced trading features
