import React from 'react';
import { useLanguage } from '../hooks/useLanguage';

const DCATable = ({ selectedCoin, currentPrice, onRemoveDCAEntry }) => {
  const { t } = useLanguage();

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 8
    }).format(value);
  };

  const formatNumber = (value, decimals = 8) => {
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: decimals
    }).format(value);
  };

  const getPLClass = (value) => {
    if (value > 0) return 'profit';
    if (value < 0) return 'loss';
    return 'neutral';
  };

  if (!selectedCoin || !selectedCoin.dcaEntries || selectedCoin.dcaEntries.length === 0) {
    return null;
  }

  return (
    <div className="card">
      <div className="card-header">
        <h4 className="card-title">{t('dcaTitle')}</h4>
        <p className="text-muted" style={{ fontSize: '0.875rem', margin: 0 }}>
          {t('supportRows')}
        </p>
      </div>

      <div className="dca-table">
        {/* Header */}
        <div className="dca-row" style={{ backgroundColor: 'var(--bg-tertiary)', fontWeight: '600' }}>
          <div>{t('entryPrice')}</div>
          <div>{t('quantity')}</div>
          <div>{t('amount')}</div>
          <div>{t('plPercent')}</div>
          <div>{t('deleteRow')}</div>
        </div>

        {/* DCA Entries */}
        {selectedCoin.dcaEntries.map((entry, index) => {
          const entryValue = entry.quantity * currentPrice;
          const entryPL = entryValue - entry.amount;
          const entryPLPercent = entry.amount > 0 ? (entryPL / entry.amount) * 100 : 0;

          return (
            <div key={entry.id} className="dca-row">
              <div>{formatCurrency(entry.price)}</div>
              <div>{formatNumber(entry.quantity)}</div>
              <div>{formatCurrency(entry.amount)}</div>
              <div className={getPLClass(entryPLPercent)}>
                {entryPLPercent >= 0 ? '+' : ''}{entryPLPercent.toFixed(2)}%
              </div>
              <div>
                {selectedCoin.dcaEntries.length > 1 && (
                  <button
                    className="btn btn-danger btn-sm"
                    onClick={() => onRemoveDCAEntry(selectedCoin.id, entry.id)}
                    title={t('deleteRow')}
                  >
                    🗑️
                  </button>
                )}
              </div>
            </div>
          );
        })}

        {/* Add more entries hint */}
        {selectedCoin.dcaEntries.length < 10 && (
          <div className="dca-row" style={{ 
            backgroundColor: 'var(--bg-primary)', 
            border: '2px dashed var(--border-color)',
            color: 'var(--text-muted)',
            fontStyle: 'italic',
            textAlign: 'center'
          }}>
            <div style={{ gridColumn: '1 / -1' }}>
              {t('addReplace')} - {10 - selectedCoin.dcaEntries.length} {t('supportRows').toLowerCase()}
            </div>
          </div>
        )}
      </div>

      {/* Summary */}
      <div style={{ 
        marginTop: 'var(--spacing-lg)', 
        padding: 'var(--spacing-md)',
        backgroundColor: 'var(--bg-tertiary)',
        borderRadius: 'var(--radius-md)'
      }}>
        <div className="flex justify-between">
          <span>{t('totalInvested')}:</span>
          <span>{formatCurrency(selectedCoin.totalInvested)}</span>
        </div>
        <div className="flex justify-between">
          <span>{t('avgPrice')}:</span>
          <span>{formatCurrency(selectedCoin.avgEntry)}</span>
        </div>
        <div className="flex justify-between">
          <span>{t('quantity')}:</span>
          <span>{formatNumber(selectedCoin.quantity)}</span>
        </div>
      </div>
    </div>
  );
};

export default DCATable;
