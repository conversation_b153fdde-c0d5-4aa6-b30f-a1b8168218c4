import React from 'react';
import { useLanguage } from '../hooks/useLanguage';

const PortfolioTable = ({ portfolio, prices, onSelectCoin, selectedCoin, portfolioSummary }) => {
  const { t } = useLanguage();

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  };

  const formatNumber = (value, decimals = 8) => {
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: decimals
    }).format(value);
  };

  const formatPercentage = (value) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
  };

  const getPLClass = (value) => {
    if (value > 0) return 'profit';
    if (value < 0) return 'loss';
    return 'neutral';
  };

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="card-title">{t('title')}</h3>
      </div>

      <div className="table-container">
        <table className="table">
          <thead>
            <tr>
              <th>{t('coin')}</th>
              <th>{t('quantity')}</th>
              <th>{t('invested')}</th>
              <th>{t('avgEntry')}</th>
              <th>{t('currentPrice')}</th>
              <th>{t('currentValue')}</th>
              <th>{t('plDollar')}</th>
              <th>{t('plPercent')}</th>
            </tr>
          </thead>
          <tbody>
            {portfolio && portfolio.length > 0 ? (
              portfolio.map((coin) => {
                const currentPrice = prices[coin.symbol]?.price || 0;
                const currentValue = coin.quantity * currentPrice;
                const pl = currentValue - coin.totalInvested;
                const plPercent = coin.totalInvested > 0 ? (pl / coin.totalInvested) * 100 : 0;

                return (
                  <tr
                    key={coin.id}
                    onClick={() => onSelectCoin(coin)}
                    style={{
                      cursor: 'pointer',
                      backgroundColor: selectedCoin?.id === coin.id ? 'var(--bg-tertiary)' : 'transparent'
                    }}
                  >
                    <td>
                      <div>
                        <div style={{ fontWeight: '600' }}>{coin.symbol}</div>
                        <div style={{ fontSize: '0.75rem', color: 'var(--text-muted)' }}>
                          {coin.name}
                        </div>
                      </div>
                    </td>
                    <td>{formatNumber(coin.quantity)}</td>
                    <td>{formatCurrency(coin.totalInvested)}</td>
                    <td>{formatCurrency(coin.avgEntry)}</td>
                    <td>{formatCurrency(currentPrice)}</td>
                    <td>{formatCurrency(currentValue)}</td>
                    <td className={getPLClass(pl)}>{formatCurrency(pl)}</td>
                    <td className={getPLClass(pl)}>{formatPercentage(plPercent)}</td>
                  </tr>
                );
              })
            ) : (
              <tr>
                <td colSpan="8" style={{ textAlign: 'center', padding: 'var(--spacing-xl)', color: 'var(--text-muted)' }}>
                  {t('addSearch')} {t('coin')}
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Portfolio Summary Footer */}
      <div className="portfolio-summary">
        <div className="summary-grid">
          <div className="summary-item">
            <div className="summary-label">{t('totalInvested')}</div>
            <div className="summary-value">
              {formatCurrency(portfolioSummary.totalInvested)}
            </div>
          </div>
          <div className="summary-item">
            <div className="summary-label">{t('totalValue')}</div>
            <div className="summary-value">
              {formatCurrency(portfolioSummary.totalValue)}
            </div>
          </div>
          <div className="summary-item">
            <div className="summary-label">{t('totalPL')}</div>
            <div className={`summary-value ${getPLClass(portfolioSummary.totalPL)}`}>
              {formatCurrency(portfolioSummary.totalPL)}
            </div>
          </div>
          <div className="summary-item">
            <div className="summary-label">{t('totalPLPercent')}</div>
            <div className={`summary-value ${getPLClass(portfolioSummary.totalPL)}`}>
              {formatPercentage(portfolioSummary.totalPLPercent)}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PortfolioTable;
