# تعليمات استخدام متتبع محفظة العملات المشفرة

## 🚀 كيفية تشغيل التطبيق

1. **تشغيل الخادم:**
   ```bash
   npm run dev
   ```

2. **فتح المتصفح:**
   - انتقل إلى: `http://localhost:3000`

## 🎯 الميزات الرئيسية

### 1. **تبديل اللغة**
- استخدم المفتاح في أعلى الصفحة للتبديل بين العربية والإنجليزية
- يتم حفظ اختيار اللغة تلقائياً

### 2. **إضافة العملات**
- اختر العملة من القائمة المنسدلة
- أدخل الكمية والمبلغ المستثمر
- اضغط "إضافة/تبديل"

### 3. **عرض التفاصيل**
- انقر على أي عملة في الجدول لعرض التفاصيل
- يمكنك تعيين أهداف البيع ووقف الخسارة

### 4. **جدول DCA**
- يظهر تلقائياً عند اختيار عملة
- يعرض جميع عمليات الشراء المتدرج
- يمكن حذف العمليات الفردية

### 5. **استيراد/تصدير البيانات**
- **التصدير:** اضغط "حفظ كـ TXT"
- **الاستيراد:** اضغط "استيراد TXT" واختر الملف

## 📊 تفسير الألوان

- **🟢 أخضر:** ربح
- **🔴 أحمر:** خسارة
- **⚪ رمادي:** متعادل

## 🔄 التحديث التلقائي

- الأسعار تتحدث كل 30 ثانية تلقائياً
- المؤشر الأخضر يدل على التحديث النشط

## 📱 التصميم المتجاوب

- يعمل على الكمبيوتر والهاتف المحمول
- تخطيط متكيف حسب حجم الشاشة

## 🛠️ استكشاف الأخطاء

### مشكلة: لا تظهر البيانات
- **الحل:** تأكد من تشغيل الخادم بـ `npm run dev`

### مشكلة: لا يعمل تبديل اللغة
- **الحل:** امسح cache المتصفح وأعد تحميل الصفحة

### مشكلة: لا تحفظ البيانات
- **الحل:** تأكد من تفعيل localStorage في المتصفح

## 📁 تنسيق ملف الاستيراد

```
SYMBOL,QUANTITY,INVESTED_AMOUNT
BTC,0.5,20000
ETH,8,18000
ADA,10000,5000
```

## 🎨 تخصيص الألوان

يمكن تعديل الألوان في ملف `src/style.css`:

```css
:root {
  --success-color: #22c55e;  /* لون الربح */
  --danger-color: #ef4444;   /* لون الخسارة */
  --accent-color: #646cff;   /* اللون الأساسي */
}
```

## 📞 الدعم

في حالة وجود مشاكل:
1. تحقق من console المتصفح للأخطاء
2. تأكد من تحديث المتصفح
3. جرب إعادة تشغيل الخادم
