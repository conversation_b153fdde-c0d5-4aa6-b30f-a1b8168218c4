import React, { useState } from 'react';
import { useLanguage } from './hooks/useLanguage';
import { useCrypto } from './hooks/useCrypto';
import LanguageToggle from './components/LanguageToggle';
import Controls from './components/Controls';
import PortfolioTable from './components/PortfolioTable';
import CoinDetails from './components/CoinDetails';
import DCATable from './components/DCATable';

function App() {
  const { t } = useLanguage();
  const {
    portfolio,
    prices,
    selectedCoin,
    setSelectedCoin,
    portfolioSummary,
    addCoin,
    removeCoin,
    updateCoinTargets,
    removeDCAEntry,
    updatePrices,
    isAutoRefresh,
    setIsAutoRefresh
  } = useCrypto();

  const [isLoading, setIsLoading] = useState(false);

  const handleAddCoin = async (symbol, quantity, investedAmount) => {
    setIsLoading(true);
    try {
      addCoin(symbol, quantity, investedAmount);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefreshPrices = async () => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      updatePrices();
    } finally {
      setIsLoading(false);
    }
  };

  const handleImportTxt = (txtContent) => {
    try {
      // Simple TXT format: SYMBOL,QUANTITY,INVESTED_AMOUNT per line
      const lines = txtContent.split('\n').filter(line => line.trim());
      lines.forEach(line => {
        const [symbol, quantity, investedAmount] = line.split(',');
        if (symbol && quantity && investedAmount) {
          addCoin(symbol.trim().toUpperCase(), parseFloat(quantity), parseFloat(investedAmount));
        }
      });
    } catch (error) {
      alert('Error importing file. Please check the format.');
    }
  };

  const handleSaveAsTxt = () => {
    const txtContent = portfolio.map(coin => 
      `${coin.symbol},${coin.quantity},${coin.totalInvested}`
    ).join('\n');
    
    const blob = new Blob([txtContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'crypto-portfolio.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleDeleteSelected = () => {
    if (selectedCoin && confirm(`${t('confirmDelete')} ${selectedCoin.symbol} ${t('fromPortfolio')}`)) {
      removeCoin(selectedCoin.id);
    }
  };

  const currentPrice = selectedCoin ? prices[selectedCoin.symbol]?.price || 0 : 0;

  return (
    <div className="container">
      {/* Header */}
      <header className="flex justify-between items-center mb-6">
        <h1>{t('title')}</h1>
        <LanguageToggle />
      </header>

      {/* Controls */}
      <Controls
        onAddCoin={handleAddCoin}
        onRefreshPrices={handleRefreshPrices}
        onImportTxt={handleImportTxt}
        onSaveAsTxt={handleSaveAsTxt}
        onDeleteSelected={handleDeleteSelected}
        availableCoins={prices}
        isLoading={isLoading}
      />

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Portfolio Table - Takes 2 columns on large screens */}
        <div className="lg:col-span-2">
          <PortfolioTable
            portfolio={portfolio}
            prices={prices}
            onSelectCoin={setSelectedCoin}
            selectedCoin={selectedCoin}
            portfolioSummary={portfolioSummary}
          />
          
          {/* DCA Table - Only show when coin is selected */}
          {selectedCoin && (
            <div className="mt-6">
              <DCATable
                selectedCoin={selectedCoin}
                currentPrice={currentPrice}
                onRemoveDCAEntry={removeDCAEntry}
              />
            </div>
          )}
        </div>

        {/* Sidebar - Coin Details */}
        <div>
          <CoinDetails
            selectedCoin={selectedCoin}
            currentPrice={currentPrice}
            onUpdateTargets={updateCoinTargets}
            isAutoRefresh={isAutoRefresh}
          />
        </div>
      </div>
    </div>
  );
}

export default App;
