import { useState, useEffect, useCallback } from 'react';

// Mock cryptocurrency data - in a real app, this would come from an API
const mockCryptoData = {
  'BTC': { symbol: 'BTC', name: 'Bitcoin', price: 43250.00 },
  'ETH': { symbol: 'ETH', name: 'Ethereum', price: 2580.50 },
  'ADA': { symbol: 'ADA', name: 'Cardano', price: 0.485 },
  'SOL': { symbol: 'SOL', name: 'Sol<PERSON>', price: 98.75 },
  'DOT': { symbol: 'DOT', name: 'Polkadot', price: 7.25 },
  'LINK': { symbol: 'LINK', name: 'Chainlink', price: 14.80 },
  'MATIC': { symbol: 'MATIC', name: 'Polygon', price: 0.92 },
  'AVAX': { symbol: 'AVAX', name: 'Avalanche', price: 36.50 }
};

// Sample portfolio data for demo
const samplePortfolio = [
  {
    id: 1,
    symbol: 'BTC',
    name: 'Bitcoin',
    quantity: 0.5,
    totalInvested: 20000,
    avgEntry: 40000,
    dcaEntries: [
      { id: 1, price: 38000, quantity: 0.3, amount: 11400 },
      { id: 2, price: 43000, quantity: 0.2, amount: 8600 }
    ],
    sellTargets: [50000, 60000, 75000],
    stopLoss: 35000
  },
  {
    id: 2,
    symbol: 'ETH',
    name: 'Ethereum',
    quantity: 8,
    totalInvested: 18000,
    avgEntry: 2250,
    dcaEntries: [
      { id: 3, price: 2100, quantity: 5, amount: 10500 },
      { id: 4, price: 2500, quantity: 3, amount: 7500 }
    ],
    sellTargets: [3000, 3500, 4000],
    stopLoss: 2000
  },
  {
    id: 3,
    symbol: 'ADA',
    name: 'Cardano',
    quantity: 10000,
    totalInvested: 5000,
    avgEntry: 0.5,
    dcaEntries: [
      { id: 5, price: 0.45, quantity: 6000, amount: 2700 },
      { id: 6, price: 0.55, quantity: 4000, amount: 2200 }
    ],
    sellTargets: [0.8, 1.0, 1.5],
    stopLoss: 0.35
  }
];

export const useCrypto = () => {
  const [portfolio, setPortfolio] = useState(() => {
    const saved = localStorage.getItem('crypto-portfolio');
    if (saved) {
      return JSON.parse(saved);
    }
    // Use sample data for demo if no saved data exists
    localStorage.setItem('crypto-portfolio', JSON.stringify(samplePortfolio));
    return samplePortfolio;
  });

  const [prices, setPrices] = useState(mockCryptoData);
  const [selectedCoin, setSelectedCoin] = useState(null);
  const [isAutoRefresh, setIsAutoRefresh] = useState(true);

  // Save portfolio to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('crypto-portfolio', JSON.stringify(portfolio));
  }, [portfolio]);

  // Mock price updates (simulate real-time data)
  const updatePrices = useCallback(() => {
    setPrices(prevPrices => {
      const newPrices = { ...prevPrices };
      Object.keys(newPrices).forEach(symbol => {
        // Simulate price fluctuation ±2%
        const fluctuation = (Math.random() - 0.5) * 0.04;
        newPrices[symbol].price *= (1 + fluctuation);
        newPrices[symbol].price = Math.round(newPrices[symbol].price * 100) / 100;
      });
      return newPrices;
    });
  }, []);

  // Auto-refresh prices every 30 seconds
  useEffect(() => {
    if (!isAutoRefresh) return;
    
    const interval = setInterval(updatePrices, 30000);
    return () => clearInterval(interval);
  }, [isAutoRefresh, updatePrices]);

  const addCoin = (symbol, quantity, investedAmount) => {
    const existingCoinIndex = portfolio.findIndex(coin => coin.symbol === symbol);
    const currentPrice = prices[symbol]?.price || 0;
    
    if (existingCoinIndex >= 0) {
      // Update existing coin
      setPortfolio(prev => {
        const updated = [...prev];
        const existing = updated[existingCoinIndex];
        const newTotalInvested = existing.totalInvested + investedAmount;
        const newTotalQuantity = existing.quantity + quantity;
        
        updated[existingCoinIndex] = {
          ...existing,
          quantity: newTotalQuantity,
          totalInvested: newTotalInvested,
          avgEntry: newTotalInvested / newTotalQuantity,
          dcaEntries: [...existing.dcaEntries, {
            id: Date.now(),
            price: currentPrice,
            quantity,
            amount: investedAmount
          }]
        };
        return updated;
      });
    } else {
      // Add new coin
      const newCoin = {
        id: Date.now(),
        symbol,
        name: prices[symbol]?.name || symbol,
        quantity,
        totalInvested: investedAmount,
        avgEntry: investedAmount / quantity,
        dcaEntries: [{
          id: Date.now(),
          price: currentPrice,
          quantity,
          amount: investedAmount
        }],
        sellTargets: [null, null, null],
        stopLoss: null
      };
      setPortfolio(prev => [...prev, newCoin]);
    }
  };

  const removeCoin = (coinId) => {
    setPortfolio(prev => prev.filter(coin => coin.id !== coinId));
    if (selectedCoin?.id === coinId) {
      setSelectedCoin(null);
    }
  };

  const updateCoinTargets = (coinId, targets, stopLoss) => {
    setPortfolio(prev => prev.map(coin => 
      coin.id === coinId 
        ? { ...coin, sellTargets: targets, stopLoss }
        : coin
    ));
  };

  const removeDCAEntry = (coinId, entryId) => {
    setPortfolio(prev => prev.map(coin => {
      if (coin.id !== coinId) return coin;
      
      const newEntries = coin.dcaEntries.filter(entry => entry.id !== entryId);
      if (newEntries.length === 0) return coin; // Keep at least one entry
      
      const newTotalInvested = newEntries.reduce((sum, entry) => sum + entry.amount, 0);
      const newTotalQuantity = newEntries.reduce((sum, entry) => sum + entry.quantity, 0);
      
      return {
        ...coin,
        dcaEntries: newEntries,
        quantity: newTotalQuantity,
        totalInvested: newTotalInvested,
        avgEntry: newTotalInvested / newTotalQuantity
      };
    }));
  };

  // Calculate portfolio summary
  const portfolioSummary = portfolio.reduce((summary, coin) => {
    const currentPrice = prices[coin.symbol]?.price || 0;
    const currentValue = coin.quantity * currentPrice;
    const pl = currentValue - coin.totalInvested;
    
    return {
      totalInvested: summary.totalInvested + coin.totalInvested,
      totalValue: summary.totalValue + currentValue,
      totalPL: summary.totalPL + pl
    };
  }, { totalInvested: 0, totalValue: 0, totalPL: 0 });

  portfolioSummary.totalPLPercent = portfolioSummary.totalInvested > 0 
    ? (portfolioSummary.totalPL / portfolioSummary.totalInvested) * 100 
    : 0;

  return {
    portfolio,
    prices,
    selectedCoin,
    setSelectedCoin,
    portfolioSummary,
    addCoin,
    removeCoin,
    updateCoinTargets,
    removeDCAEntry,
    updatePrices,
    isAutoRefresh,
    setIsAutoRefresh
  };
};
